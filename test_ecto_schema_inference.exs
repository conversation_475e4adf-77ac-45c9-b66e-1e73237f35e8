#!/usr/bin/env elixir

# Test script to verify Ecto schema inference functionality
# This script demonstrates the basic usage of schema(EctoSchema) in contracts

# Load the test environment
Mix.install([{:drops, path: "."}, {:ecto, "~> 3.10"}])

# Define a simple Ecto schema for testing
defmodule UserSchema do
  use Ecto.Schema

  schema "users" do
    field(:name, :string)
    field(:email, :string)
    field(:age, :integer)

    timestamps()
  end
end

# Test 1: Basic schema inference
defmodule BasicUserContract do
  use Drops.Contract

  schema(UserSchema)
end

IO.puts("=== Test 1: Basic Ecto Schema Inference ===")

# Test valid data (note: timestamps are excluded by default)
case BasicUserContract.conform(%{name: "<PERSON>", email: "<EMAIL>", age: 30}) do
  {:ok, result} ->
    IO.puts("✓ Valid data conformed successfully:")
    IO.inspect(result)
  {:error, errors} ->
    IO.puts("✗ Unexpected error:")
    IO.inspect(errors)
end

# Test missing required field
case BasicUserContract.conform(%{email: "<EMAIL>", age: 30}) do
  {:ok, result} ->
    IO.puts("✗ Should have failed for missing required field")
  {:error, errors} ->
    IO.puts("✓ Correctly rejected missing required field:")
    IO.inspect(Enum.map(errors, &to_string/1))
end

# Test invalid field type
case BasicUserContract.conform(%{name: 123, email: "<EMAIL>", age: 30}) do
  {:ok, result} ->
    IO.puts("✗ Should have failed for invalid field type")
  {:error, errors} ->
    IO.puts("✓ Correctly rejected invalid field type:")
    IO.inspect(Enum.map(errors, &to_string/1))
end

IO.puts("\n=== Test 2: Ecto Schema Inference with Options ===")

# Test 2: Schema inference with atomize option
defmodule AtomizedUserContract do
  use Drops.Contract

  schema(UserSchema, atomize: true)
end

# Test string keys with atomize option
case AtomizedUserContract.conform(%{"name" => "Jane", "email" => "<EMAIL>", "age" => 25}) do
  {:ok, result} ->
    IO.puts("✓ String keys converted to atoms successfully:")
    IO.inspect(result)
  {:error, errors} ->
    IO.puts("✗ Unexpected error:")
    IO.inspect(errors)
end

IO.puts("\n=== All tests completed! ===")
